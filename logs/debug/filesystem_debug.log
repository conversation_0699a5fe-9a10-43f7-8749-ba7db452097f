=== Filesystem Scanner Started ===
Scanning filesystem under: /lilyspark

=== Directory Structure (depth=3 with counts) ===
DIR: app (files: 1)
DIR: app/build (files: 9)
DIR: app/build/CMakeFiles (files: 5)
DIR: app/build/plutosvg-install
DIR: app/build/src (files: 9)
DIR: app/build_files (files: 1)
DIR: app/src (files: 14)
DIR: app/src/CMakeFiles
DIR: app/src/Drive
DIR: app/src/cmake (files: 1)
DIR: app/src/docker_build
DIR: app/src/docs (files: 10)
DIR: app/src/kern (files: 3)
DIR: app/src/lib
DIR: app/src/log
DIR: app/src/logs
DIR: app/src/patches (files: 1)
DIR: app/src/setup-scripts (files: 25)
DIR: app/src/src (files: 1)
DIR: app/src/test_imports
DIR: compiler
DIR: compiler/bin (files: 2)
DIR: compiler/include
DIR: compiler/include/llvm (files: 8)
DIR: compiler/include/llvm-c (files: 28)
DIR: compiler/lib (files: 4)
DIR: compiler/lib/clang
DIR: compiler/lib/cmake
DIR: dist
DIR: etc (files: 1)
DIR: etc/profile.d (files: 4)
DIR: glibc
DIR: glibc/bin (files: 17)
DIR: glibc/include
DIR: glibc/lib (files: 57)
DIR: glibc/lib/locale
DIR: glibc/sbin (files: 6)
DIR: home
DIR: lib (files: 1)
DIR: lib/apk
DIR: lib/apk/db (files: 4)
DIR: lib/apk/exec
DIR: log (files: 3)
DIR: opt
DIR: opt/lib
DIR: opt/lib/audio
DIR: opt/lib/database
DIR: opt/lib/driver
DIR: opt/lib/graphics
DIR: opt/lib/java
DIR: opt/lib/media
DIR: opt/lib/python
DIR: opt/lib/sdl3
DIR: opt/lib/sys
DIR: opt/lib/vulkan
DIR: snapshots
DIR: src
DIR: sysroot
DIR: sysroot/asm
DIR: sysroot/kern
DIR: sysroot/os
DIR: sysroot/os/bin
DIR: tmp
DIR: usr
DIR: usr/bin (files: 8)
DIR: usr/debug
DIR: usr/debug/bin (files: 2)
DIR: usr/include (files: 231)
DIR: usr/include/CUnit (files: 10)
DIR: usr/include/FLAC (files: 9)
DIR: usr/include/FLAC++ (files: 5)
DIR: usr/include/GL (files: 4)
DIR: usr/include/X11 (files: 41)
DIR: usr/include/alsa (files: 31)
DIR: usr/include/arch
DIR: usr/include/arpa (files: 6)
DIR: usr/include/asm (files: 37)
DIR: usr/include/asm-generic (files: 37)
DIR: usr/include/atomic_ops (files: 4)
DIR: usr/include/avif (files: 1)
DIR: usr/include/bits (files: 35)
DIR: usr/include/blkid (files: 1)
DIR: usr/include/brotli (files: 5)
DIR: usr/include/btrfs (files: 8)
DIR: usr/include/c++
DIR: usr/include/cairo (files: 15)
DIR: usr/include/dbus-1.0
DIR: usr/include/e2p (files: 1)
DIR: usr/include/editline (files: 1)
DIR: usr/include/eigen3 (files: 1)
DIR: usr/include/et (files: 1)
DIR: usr/include/ext2fs (files: 11)
DIR: usr/include/fontconfig (files: 3)
DIR: usr/include/fortify (files: 8)
DIR: usr/include/freetype2 (files: 1)
DIR: usr/include/gio-unix-2.0
DIR: usr/include/glib-2.0 (files: 4)
DIR: usr/include/graphite2 (files: 4)
DIR: usr/include/harfbuzz (files: 41)
DIR: usr/include/libdvbv5 (files: 47)
DIR: usr/include/libfdisk (files: 1)
DIR: usr/include/liblastlog2 (files: 1)
DIR: usr/include/libltdl (files: 3)
DIR: usr/include/libmnl (files: 1)
DIR: usr/include/libmodplug (files: 4)
DIR: usr/include/libmount (files: 1)
DIR: usr/include/libpng16 (files: 3)
DIR: usr/include/libsmartcols (files: 1)
DIR: usr/include/libucontext (files: 1)
DIR: usr/include/liburing (files: 5)
DIR: usr/include/libusb-1.0 (files: 1)
DIR: usr/include/libxml2
DIR: usr/include/linux (files: 552)
DIR: usr/include/lzma (files: 14)
DIR: usr/include/misc (files: 5)
DIR: usr/include/mtd (files: 5)
DIR: usr/include/net (files: 4)
DIR: usr/include/netinet (files: 11)
DIR: usr/include/netpacket (files: 1)
DIR: usr/include/ogg (files: 3)
DIR: usr/include/openssl (files: 139)
DIR: usr/include/opus (files: 7)
DIR: usr/include/pcap (files: 15)
DIR: usr/include/pci (files: 4)
DIR: usr/include/perf (files: 1)
DIR: usr/include/pipewire-0.3
DIR: usr/include/pixman-1 (files: 2)
DIR: usr/include/pkgconf
DIR: usr/include/portaudiocpp (files: 19)
DIR: usr/include/pulse (files: 32)
DIR: usr/include/python3.12 (files: 74)
DIR: usr/include/rdma (files: 27)
DIR: usr/include/readline (files: 8)
DIR: usr/include/scsi (files: 9)
DIR: usr/include/selinux (files: 7)
DIR: usr/include/sepol (files: 25)
DIR: usr/include/sound (files: 17)
DIR: usr/include/spa-0.2
DIR: usr/include/ss (files: 2)
DIR: usr/include/sys (files: 73)
DIR: usr/include/textstyle (files: 3)
DIR: usr/include/unicode (files: 197)
DIR: usr/include/usbip (files: 6)
DIR: usr/include/uuid (files: 1)
DIR: usr/include/valgrind (files: 68)
DIR: usr/include/video (files: 3)
DIR: usr/include/vk_video (files: 9)
DIR: usr/include/vorbis (files: 3)
DIR: usr/include/vulkan (files: 35)
DIR: usr/include/wayland-protocols (files: 50)
DIR: usr/include/webp (files: 6)
DIR: usr/include/xcb (files: 41)
DIR: usr/include/xen (files: 4)
DIR: usr/include/xfs (files: 12)
DIR: usr/include/xkbcommon (files: 7)
DIR: usr/lib (files: 19)
DIR: usr/lib/dri
DIR: usr/lib/runtime
DIR: usr/lib/xorg
DIR: usr/local
DIR: usr/local/bin (files: 1)
DIR: usr/local/lib
DIR: usr/local/sbin
DIR: usr/local/share
DIR: usr/sbin
DIR: usr/share
DIR: usr/share/vulkan
DIR: usr/x11
DIR: usr/x11/include
DIR: usr/{bin,lib,include,share}
DIR: var
DIR: var/log
DIR: var/log/debug (files: 3)

=== Detailed File Counts ===
  - Binaries: 10
  - Shared/Static Libraries: 502
  - Config Files: 5
  - Header Files: 5966

=== Binary Analysis ===
Top 10 largest binaries:
  - 68K: usr/bin/file
  - 196K: compiler/bin/clang++-16
  - 196K: compiler/bin/clang-16
  - 260K: usr/bin/lsof
  - 268K: usr/bin/ltrace
  - 460K: usr/bin/objdump
  - 1.2M: usr/bin/strace
  - 3.0M: usr/bin/simplehttpserver
  - 3.8M: usr/bin/perf
  - 11M: usr/bin/gdb

=== Library Dependencies ===
Checking dependencies for sample binaries:
  - compiler/bin/clang-16:
    	/lib/ld-musl-aarch64.so.1 (0xffffa9034000)
    	libclang-cpp.so.16 => /lilyspark/compiler/lib/libclang-cpp.so.16 (0xffffa5600000)
    	libLLVM-16.so => /usr/lib/llvm16/lib/libLLVM-16.so (0xffff9d800000)
    	libstdc++.so.6 => /lilyspark/usr/lib/libstdc++.so.6 (0xffff9d400000)
    	libgcc_s.so.1 => /lilyspark/usr/lib/libgcc_s.so.1 (0xffffa8fc2000)
    	libc.musl-aarch64.so.1 => /lib/ld-musl-aarch64.so.1 (0xffffa9034000)
    	libffi.so.8 => /usr/lib/libffi.so.8 (0xffffa8fa1000)
    	libz.so.1 => /usr/lib/libz.so.1 (0xffffa8f70000)
    	libzstd.so.1 => /lilyspark/usr/lib/libzstd.so.1 (0xffffa8ebf000)
    	libxml2.so.2 => /usr/lib/libxml2.so.2 (0xffff9d6de000)
    	liblzma.so.5 => /lilyspark/usr/lib/liblzma.so.5 (0xffffa8e6e000)
  - compiler/bin/clang++-16:
    	/lib/ld-musl-aarch64.so.1 (0xffff92f7c000)
    	libclang-cpp.so.16 => /lilyspark/compiler/lib/libclang-cpp.so.16 (0xffff8f600000)
    	libLLVM-16.so => /usr/lib/llvm16/lib/libLLVM-16.so (0xffff87800000)
    	libstdc++.so.6 => /lilyspark/usr/lib/libstdc++.so.6 (0xffff87400000)
    	libgcc_s.so.1 => /lilyspark/usr/lib/libgcc_s.so.1 (0xffff92f0a000)
    	libc.musl-aarch64.so.1 => /lib/ld-musl-aarch64.so.1 (0xffff92f7c000)
    	libffi.so.8 => /usr/lib/libffi.so.8 (0xffff92ee9000)
    	libz.so.1 => /usr/lib/libz.so.1 (0xffff92eb8000)
    	libzstd.so.1 => /lilyspark/usr/lib/libzstd.so.1 (0xffff8f54f000)
    	libxml2.so.2 => /usr/lib/libxml2.so.2 (0xffff876de000)
    	liblzma.so.5 => /lilyspark/usr/lib/liblzma.so.5 (0xffff92e67000)

=== File Type Breakdown ===
Compiler binary types:
  - compiler/bin/clang-16: ELF 64-bit LSB pie executable
  - compiler/bin/clang++-16: ELF 64-bit LSB pie executable

=== Library Symbols ===
Sample library exports:
  - compiler/lib/libRemarks.so.16:
    0000000000000660 T LLVMRemarkVersion
    0000000000000668 T _fini
    00000000000004f8 T _init

=== Detailed Disk Usage ===
Total size: 1.1G

  - dist                     4.0K ( 0%)
  - home                     4.0K ( 0%)
  - snapshots                4.0K ( 0%)
  - src                      4.0K ( 0%)
  - tmp                      4.0K ( 0%)
  - sysroot                   20K ( 0%)
  - var                       24K ( 0%)
  - etc                       28K ( 0%)
  - log                       36K ( 0%)
  - lib                      1.6M ( 0%)
  - app                       31M ( 2%)
  - glibc                     72M ( 6%)
  - compiler                  96M ( 8%)
  - opt                      294M (26%)
  - usr                      600M (54%)

=== Permission Analysis ===
World-writable files:

=== Filesystem Scanner Complete ===

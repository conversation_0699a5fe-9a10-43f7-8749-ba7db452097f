=== DOCKER DEBUG SUMMARY REPORT ===
Generated on: Wed Sep  3 14:55:55 PDT 2025

=== DOCKER RUNTIME DEBUG ===
 Labels:
  com.docker.desktop.address=unix:///Users/<USER>/Library/Containers/com.docker.docker/Data/docker-cli.sock
 Experimental: false
 Insecure Registries:
  hubproxy.docker.internal:5555
  ::1/128
  *********/8
 Live Restore Enabled: false

WARNING: DOCKER_INSECURE_NO_IPTABLES_RAW is set
2. Checking if image exists...
REPOSITORY                         TAG       IMAGE ID       CREATED         SIZE
mostsignificant/simplehttpserver   latest    c374f43775ac   6 minutes ago   5.99GB
3. Testing container creation (dry run)...
96049b252ff107875b54a6ed13a3c9256a27f38ef4801e76fa325130352fe0bb
Container creation successful
sdl3-debug-test
4. Running container with verbose logging...
Container exit code: 139
SEGMENTATION FAULT DETECTED (exit code 139)

=== FILESYSTEM STATUS ===
  - dist                     4.0K ( 0%)
  - home                     4.0K ( 0%)
  - snapshots                4.0K ( 0%)
  - src                      4.0K ( 0%)
  - tmp                      4.0K ( 0%)
  - sysroot                   20K ( 0%)
  - var                       24K ( 0%)
  - etc                       28K ( 0%)
  - log                       36K ( 0%)
  - lib                      1.6M ( 0%)
  - app                       31M ( 2%)
  - glibc                     72M ( 6%)
  - compiler                  96M ( 8%)
  - opt                      294M (26%)
  - usr                      600M (54%)

=== Permission Analysis ===
World-writable files:

=== Filesystem Scanner Complete ===

=== BINARY VALIDATION ===
🔍 Validating: /lilyspark/app/simplehttpserver
\033[0;31m✗ ERROR: File not found\033[0m

=== RECOMMENDATIONS ===
• SEGMENTATION FAULT detected - check binary compatibility and dependencies
• Run 'make debug-docker-gdb' for detailed crash analysis
• Run 'make debug-docker-strace' to trace system calls

=== DEBUG FILES GENERATED ===
total 48
drwxr-xr-x@ 6 <USER>  <GROUP>   192 Sep  3 14:55 .
drwxr-xr-x@ 5 <USER>  <GROUP>   160 Sep  3 14:55 ..
-rw-r--r--@ 1 <USER>  <GROUP>    92 Sep  3 14:55 binary_debug.log
-rw-r--r--@ 1 <USER>  <GROUP>  1956 Sep  3 14:55 debug_summary.log
-rw-r--r--@ 1 <USER>  <GROUP>  4482 Sep  3 14:55 docker_debug.log
-rw-r--r--@ 1 <USER>  <GROUP>  8135 Sep  3 14:55 filesystem_debug.log

#Universal SDL3

Hello, welcome to a Universal SDL3 Application project repo!!!! Ever
since I began working with this library, one of the biggest hassles is
to get dynamic linking working. And so hence, was born this Universal SDL project.

This is a highly experimental project, meaning there's a lot of testing
of it required still. Already, there are issues under the hood if you
run the build-docker-debug make command, and that may interfere with
just exactly what code you can run.

My aims with this project is to provide cross-platform development for
Windows, Mac, and Linux without needing to configure each platform build
and face the problem of discrepancies between the three OSses.

I hope this runs on your machine, as this is the point of a project like this.
Do not expect it to be stable to write code to said window and rely upon
other libraries. This is far from where I want this codebase to be
